import { FC, useRef, useEffect, useState } from 'react';
import TextInput from '@components/TextInput';
import PhoneInput from '@components/PhoneInput';
import Button from '@components/Button';
import QRCode from '@components/QRCode';
import { Document, Page, Text, View, Image, pdf } from '@react-pdf/renderer';
import styles from './BusinessCard.module.css';
import backImage from '../../assets/BC_Back_Horizontal_FINAL.png';
import { PDF_STYLES } from './constants';
import { registerFonts } from '../../utils/fontUtils';
import { useFormValidation } from '../../hooks/useFormValidation';
import {
  generateVCard,
  convertSVGToPNG,
  downloadPDFFile,
} from '../../utils/pdf';
import { BusinessCardProps } from './BusinessCard.types';

const BusinessCard: FC<BusinessCardProps> = ({
  onGenerate,
  onError,
  initialData,
}) => {
  const { formData, errors, handleInputChange, validateForm } =
    useFormValidation({ initialData });

  console.log('Initial Data:', initialData);

  const [qrValue, setQrValue] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [fontsLoaded, setFontsLoaded] = useState(false);
  const [fontError, setFontError] = useState<string | null>(null);
  const qrCodeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let mounted = true;

    const loadFonts = async () => {
      try {
        await registerFonts();
        if (mounted) {
          setFontsLoaded(true);
          setFontError(null);
        }
      } catch (error) {
        console.error('Failed to load fonts:', error);
        if (mounted) {
          setFontError('Failed to load fonts. Please try again later.');
          onError?.(
            error instanceof Error ? error : new Error('Failed to load fonts'),
          );
        }
      }
    };

    loadFonts();
    return () => {
      mounted = false;
    };
  }, [onError]);

  const downloadPDF = async () => {
    if (!validateForm()) return;
    if (!fontsLoaded) {
      if (fontError) {
        alert(fontError);
      } else {
        alert('Fonts are still loading. Please wait a moment and try again.');
      }
      return;
    }

    try {
      setIsGenerating(true);
      const vcardString = generateVCard(formData);
      setQrValue(vcardString);

      // Wait for QR code to render
      await new Promise(resolve => setTimeout(resolve, 500));

      if (!qrCodeRef.current) {
        throw new Error('QR code element not found');
      }

      const svgElement = qrCodeRef.current.querySelector('svg');
      if (!svgElement) {
        throw new Error('QR code SVG element not found');
      }

      const qrCodeDataURL = await convertSVGToPNG(svgElement);

      const pdfBlob = await pdf(
        <Document>
          <Page size={[292, 400]} style={PDF_STYLES.page}>
            <View style={PDF_STYLES.frontSide}>
              <Image src={backImage} style={PDF_STYLES.frontImage} />
            </View>
            <View style={PDF_STYLES.backSide}>
              <Text style={PDF_STYLES.name}>{formData.firstName}</Text>
              <Text style={PDF_STYLES.name}>{formData.lastName}</Text>
              <Text style={PDF_STYLES.titleText}>{formData.title},</Text>
              <Text style={PDF_STYLES.organizationText}>
                {formData.organization}
              </Text>
              <Text style={PDF_STYLES.contactText}>{formData.email}</Text>
              <Text style={PDF_STYLES.contactText}>{formData.phoneNumber}</Text>
              <Image src={qrCodeDataURL} style={PDF_STYLES.qrCode} />
            </View>
          </Page>
        </Document>,
      ).toBlob();

      const pdfUrl = URL.createObjectURL(pdfBlob);
      const fileName = `${formData.firstName}-${formData.lastName}-business-card.pdf`;
      downloadPDFFile(pdfUrl, fileName);

      onGenerate?.(formData);
    } catch (error) {
      console.error('Error generating PDF:', error);
      onError?.(error instanceof Error ? error : new Error('Unknown error'));
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className={styles.businessCardContainer}>
      <div className={styles.formContainer}>
        <div className={styles.inputGroup}>
          <TextInput
            label="First Name"
            value={formData.firstName}
            error={errors.firstName}
            onChange={e =>
              handleInputChange(
                'firstName',
                (e.target as HTMLInputElement).value,
              )
            }
            required
          />
        </div>
        <div className={styles.inputGroup}>
          <TextInput
            label="Last Name"
            value={formData.lastName}
            error={errors.lastName}
            onChange={e =>
              handleInputChange(
                'lastName',
                (e.target as HTMLInputElement).value,
              )
            }
            required
          />
        </div>
        <div className={styles.inputGroup}>
          <PhoneInput
            label="Phone Number"
            value={formData.phoneNumber}
            // error={errors.phoneNumber}
            onChange={value => handleInputChange('phoneNumber', value)}
          />
        </div>
        <div className={styles.inputGroup}>
          <TextInput
            label="Email"
            value={formData.email}
            error={errors.email}
            onChange={e =>
              handleInputChange('email', (e.target as HTMLInputElement).value)
            }
            type="email"
            required
          />
        </div>
        <div className={styles.inputGroup}>
          <TextInput
            label="Organization"
            value={formData.organization}
            error={errors.organization}
            onChange={e =>
              handleInputChange(
                'organization',
                (e.target as HTMLInputElement).value,
              )
            }
            required
          />
        </div>
        <div className={styles.inputGroup}>
          <TextInput
            label="Job Title"
            value={formData.title}
            error={errors.title}
            onChange={e =>
              handleInputChange('title', (e.target as HTMLInputElement).value)
            }
            required
          />
        </div>

        <div className={styles.buttonContainer}>
          <Button
            label={
              isGenerating ? 'Generating PDF....' : 'Download Business Card PDF'
            }
            onClick={downloadPDF}
            isDisabled={isGenerating}
            theme="primary"
            size="large"
            isFullWidth={true}
            type="button"
          />
        </div>
      </div>
      <div ref={qrCodeRef} className={styles.qrCodeContainer}>
        {qrValue && <QRCode url={qrValue} size="256" />}
      </div>
    </div>
  );
};

export default BusinessCard;
